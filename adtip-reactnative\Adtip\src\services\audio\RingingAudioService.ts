import { Platform, NativeModules } from 'react-native';

/**
 * Simple audio service for playing ringing sounds during calls
 * Uses native audio capabilities to play ringing sound in earpiece
 */
class RingingAudioService {
  private static instance: RingingAudioService;
  private isRinging: boolean = false;
  private ringingInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  static getInstance(): RingingAudioService {
    if (!RingingAudioService.instance) {
      RingingAudioService.instance = new RingingAudioService();
    }
    return RingingAudioService.instance;
  }

  /**
   * Start playing ringing sound in earpiece
   * This creates a simple beep pattern to simulate ringing
   */
  startRinging(): void {
    if (this.isRinging) {
      console.log('[RingingAudioService] Already ringing, ignoring start request');
      return;
    }

    console.log('[RingingAudioService] Starting ringing sound');
    this.isRinging = true;

    // Create a simple ringing pattern using system sounds
    this.playRingingPattern();
  }

  /**
   * Stop the ringing sound
   */
  stopRinging(): void {
    if (!this.isRinging) {
      return;
    }

    console.log('[RingingAudioService] Stopping ringing sound');
    this.isRinging = false;

    if (this.ringingInterval) {
      clearInterval(this.ringingInterval);
      this.ringingInterval = null;
    }
  }

  /**
   * Check if currently ringing
   */
  isCurrentlyRinging(): boolean {
    return this.isRinging;
  }

  /**
   * Play a simple ringing pattern
   * This creates a repeating beep sound to simulate phone ringing
   */
  private playRingingPattern(): void {
    // Create a simple pattern: beep, pause, beep, pause, longer pause, repeat
    let beepCount = 0;
    const maxBeeps = 2; // Two beeps per cycle
    const beepDuration = 500; // 500ms beep
    const shortPause = 300; // 300ms pause between beeps
    const longPause = 1500; // 1.5s pause between cycles

    const playBeep = () => {
      if (!this.isRinging) return;

      // Play system beep sound
      this.playSystemBeep();
      beepCount++;

      if (beepCount < maxBeeps) {
        // Short pause before next beep in cycle
        setTimeout(playBeep, beepDuration + shortPause);
      } else {
        // Reset count and wait for next cycle
        beepCount = 0;
        setTimeout(playBeep, beepDuration + longPause);
      }
    };

    // Start the pattern
    playBeep();
  }

  /**
   * Play a system beep sound
   * This uses platform-specific methods to play a simple beep
   */
  private playSystemBeep(): void {
    try {
      if (Platform.OS === 'android') {
        // On Android, we can use the ToneGenerator or system sounds
        // For now, we'll use a simple approach
        this.playAndroidBeep();
      } else if (Platform.OS === 'ios') {
        // On iOS, we can use AudioServicesPlaySystemSound
        this.playIOSBeep();
      }
    } catch (error) {
      console.error('[RingingAudioService] Error playing system beep:', error);
    }
  }

  /**
   * Play beep sound on Android
   */
  private playAndroidBeep(): void {
    try {
      // Try to use native module if available
      const { IncomingCallModule } = NativeModules;
      if (IncomingCallModule && IncomingCallModule.playBeep) {
        IncomingCallModule.playBeep();
      } else {
        // Fallback: log the beep (in a real implementation, you might use react-native-sound)
        console.log('[RingingAudioService] 🔊 BEEP (Android)');
      }
    } catch (error) {
      console.error('[RingingAudioService] Android beep error:', error);
    }
  }

  /**
   * Play beep sound on iOS
   */
  private playIOSBeep(): void {
    try {
      // Try to use native module if available
      const { AdtipCallKitManager } = NativeModules;
      if (AdtipCallKitManager && AdtipCallKitManager.playBeep) {
        AdtipCallKitManager.playBeep();
      } else {
        // Fallback: log the beep (in a real implementation, you might use react-native-sound)
        console.log('[RingingAudioService] 🔊 BEEP (iOS)');
      }
    } catch (error) {
      console.error('[RingingAudioService] iOS beep error:', error);
    }
  }

  /**
   * Cleanup method to ensure ringing is stopped
   */
  cleanup(): void {
    this.stopRinging();
  }
}

export default RingingAudioService;
