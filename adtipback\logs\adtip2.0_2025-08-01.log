info: Aug-01-2025 22:18:44: 	Performance monitoring started
info: Aug-01-2025 22:19:03: 	Starting database health monitoring
info: Aug-01-2025 22:19:07: 	Database health check passed (4307ms)
info: Aug-01-2025 22:19:34: 	Database health check passed (385ms)
info: Aug-01-2025 22:20:03: 	Connection pool stats:
info: Aug-01-2025 22:20:05: 	Database health check passed (1588ms)
error: Aug-01-2025 22:20:31: 	FAILED REQUEST [04ypzr]: POST /api/check-app-version returned 426
info: Aug-01-2025 22:20:34: 	Database health check passed (401ms)
info: Aug-01-2025 22:21:03: 	Connection pool stats:
info: Aug-01-2025 22:21:04: 	Database health check passed (396ms)
info: Aug-01-2025 22:21:26: 	Performance monitoring started
info: Aug-01-2025 22:21:29: 	Starting database health monitoring
info: Aug-01-2025 22:21:30: 	Database health check passed (1217ms)
error: Aug-01-2025 22:21:38: 	FAILED REQUEST [lpsxd]: POST /api/check-app-version returned 426
info: Aug-01-2025 22:21:59: 	Database health check passed (407ms)
info: Aug-01-2025 22:22:25: 	Performance monitoring started
info: Aug-01-2025 22:22:27: 	Starting database health monitoring
info: Aug-01-2025 22:22:29: 	Database health check passed (1396ms)
info: Aug-01-2025 22:22:57: 	Database health check passed (245ms)
info: Aug-01-2025 22:23:27: 	Connection pool stats:
info: Aug-01-2025 22:23:27: 	Database health check passed (70ms)
info: Aug-01-2025 22:23:58: 	Database health check passed (537ms)
info: Aug-01-2025 22:24:27: 	Connection pool stats:
info: Aug-01-2025 22:24:27: 	Database health check passed (149ms)
info: Aug-01-2025 22:24:57: 	Database health check passed (238ms)
error: Aug-01-2025 22:25:26: 	FAILED REQUEST [93jyon]: POST /api/ping returned 426
info: Aug-01-2025 22:25:27: 	Connection pool stats:
info: Aug-01-2025 22:25:28: 	Database health check passed (310ms)
info: Aug-01-2025 22:25:59: 	Performance monitoring started
info: Aug-01-2025 22:26:01: 	Starting database health monitoring
info: Aug-01-2025 22:26:04: 	Database health check passed (3356ms)
error: Aug-01-2025 22:26:14: 	FAILED REQUEST [w1ezvs]: POST /api/ping returned 426
info: Aug-01-2025 22:26:31: 	Database health check passed (109ms)
info: Aug-01-2025 22:27:01: 	Connection pool stats:
info: Aug-01-2025 22:27:01: 	Database health check passed (58ms)
info: Aug-01-2025 22:27:31: 	Database health check passed (112ms)
info: Aug-01-2025 22:28:01: 	Connection pool stats:
info: Aug-01-2025 22:28:01: 	Database health check passed (58ms)
info: Aug-01-2025 22:28:31: 	Database health check passed (58ms)
info: Aug-01-2025 22:29:01: 	Connection pool stats:
info: Aug-01-2025 22:29:01: 	Database health check passed (86ms)
info: Aug-01-2025 22:29:31: 	Database health check passed (57ms)
info: Aug-01-2025 22:30:01: 	Connection pool stats:
info: Aug-01-2025 22:30:01: 	Database health check passed (68ms)
error: Aug-01-2025 22:30:28: 	FAILED REQUEST [wa73u8]: POST /api/ping returned 426
info: Aug-01-2025 22:30:31: 	Database health check passed (57ms)
info: Aug-01-2025 22:31:01: 	Connection pool stats:
info: Aug-01-2025 22:31:02: 	Database health check passed (454ms)
info: Aug-01-2025 22:31:33: 	Database health check passed (1789ms)
info: Aug-01-2025 22:32:01: 	Connection pool stats:
info: Aug-01-2025 22:32:02: 	Database health check passed (683ms)
info: Aug-01-2025 22:32:31: 	Database health check passed (58ms)
info: Aug-01-2025 22:33:01: 	Connection pool stats:
info: Aug-01-2025 22:33:01: 	Database health check passed (228ms)
info: Aug-01-2025 22:33:33: 	Database health check passed (1734ms)
info: Aug-01-2025 22:34:01: 	Connection pool stats:
info: Aug-01-2025 22:34:02: 	Database health check passed (674ms)
info: Aug-01-2025 22:34:32: 	Database health check passed (676ms)
info: Aug-01-2025 22:35:01: 	Connection pool stats:
info: Aug-01-2025 22:35:02: 	Database health check passed (622ms)
info: Aug-01-2025 22:35:32: 	Database health check passed (511ms)
info: Aug-01-2025 22:36:01: 	Connection pool stats:
info: Aug-01-2025 22:36:01: 	Database health check passed (61ms)
info: Aug-01-2025 22:36:31: 	Database health check passed (141ms)
info: Aug-01-2025 22:37:01: 	Connection pool stats:
info: Aug-01-2025 22:37:01: 	Database health check passed (121ms)
info: Aug-01-2025 22:37:32: 	Database health check passed (351ms)
info: Aug-01-2025 22:38:01: 	Connection pool stats:
info: Aug-01-2025 22:38:02: 	Database health check passed (396ms)
info: Aug-01-2025 22:38:32: 	Database health check passed (421ms)
info: Aug-01-2025 22:39:01: 	Connection pool stats:
info: Aug-01-2025 22:39:02: 	Database health check passed (1186ms)
info: Aug-01-2025 22:39:31: 	Database health check passed (142ms)
info: Aug-01-2025 22:40:01: 	Connection pool stats:
info: Aug-01-2025 22:40:02: 	Database health check passed (1118ms)
info: Aug-01-2025 22:46:03: 	Performance monitoring started
info: Aug-01-2025 22:46:43: 	Starting database health monitoring
info: Aug-01-2025 22:46:44: 	Database health check passed (642ms)
info: Aug-01-2025 22:47:14: 	Database health check passed (482ms)
info: Aug-01-2025 22:47:43: 	Connection pool stats:
info: Aug-01-2025 22:47:43: 	Database health check passed (48ms)
info: Aug-01-2025 22:48:13: 	Database health check passed (35ms)
info: Aug-01-2025 22:48:43: 	Connection pool stats:
info: Aug-01-2025 22:48:44: 	Database health check passed (1211ms)
info: Aug-01-2025 22:49:08: 	OTP cleared for **********
info: Aug-01-2025 22:49:08: 	OTP verified successfully for **********
warn: Aug-01-2025 22:49:10: 	SLOW REQUEST [eqw3pt]: POST /api/otpverify took 2638ms
info: Aug-01-2025 22:49:13: 	Database health check passed (322ms)
warn: Aug-01-2025 22:49:28: 	SLOW REQUEST [acfybl]: GET /api/getvideos/58422/0/1 took 3877ms
warn: Aug-01-2025 22:49:36: 	SLOW REQUEST [qjo59]: GET /api/getvideos/58422/0/1 took 6549ms
info: Aug-01-2025 22:49:43: 	Connection pool stats:
info: Aug-01-2025 22:49:43: 	Database health check passed (40ms)
info: Aug-01-2025 22:50:13: 	Database health check passed (241ms)
warn: Aug-01-2025 22:50:16: 	SLOW REQUEST [w7gsbi]: GET /api/getvideos/58422/0/1 took 7600ms
warn: Aug-01-2025 22:50:24: 	SLOW REQUEST [qijv9f]: GET /api/getvideos/58422/0/1 took 5138ms
warn: Aug-01-2025 22:50:26: 	SLOW REQUEST [0zekpk]: GET /api/getvideos/58422/0/1 took 2922ms
info: Aug-01-2025 22:50:43: 	Connection pool stats:
info: Aug-01-2025 22:50:44: 	Database health check passed (424ms)
error: Aug-01-2025 22:50:56: 	FAILED REQUEST [6a14xr]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 22:50:59: 	SLOW REQUEST [z14xch]: POST /api/users took 3604ms
info: Aug-01-2025 22:51:14: 	Database health check passed (779ms)
warn: Aug-01-2025 22:51:33: 	SLOW REQUEST [tmqz5k]: POST /api/adtipcall took 5121ms
info: Aug-01-2025 22:51:43: 	Connection pool stats:
info: Aug-01-2025 22:51:43: 	Database health check passed (320ms)
warn: Aug-01-2025 22:51:56: 	SLOW REQUEST [26glg1i]: POST /api/voice-call took 3096ms
warn: Aug-01-2025 22:52:01: 	SLOW REQUEST [m5u2ed]: GET /api/get-fcm-token/unknown took 2528ms
error: Aug-01-2025 22:52:01: 	FAILED REQUEST [m5u2ed]: GET /api/get-fcm-token/unknown returned 404
info: Aug-01-2025 22:52:13: 	Database health check passed (219ms)
info: Aug-01-2025 22:52:43: 	Connection pool stats:
info: Aug-01-2025 22:52:43: 	Database health check passed (80ms)
error: Aug-01-2025 22:52:57: 	FAILED REQUEST [48fxas]: GET /api/check-premium/63779 returned 400
warn: Aug-01-2025 22:53:03: 	SLOW REQUEST [etq09]: POST /api/users took 5873ms
info: Aug-01-2025 22:53:13: 	Database health check passed (209ms)
info: Aug-01-2025 22:53:43: 	Connection pool stats:
info: Aug-01-2025 22:53:43: 	Database health check passed (139ms)
error: Aug-01-2025 22:54:01: 	FAILED REQUEST [1qeieg]: GET /api/check-premium/63779 returned 400
warn: Aug-01-2025 22:54:05: 	SLOW REQUEST [dc83nb]: POST /api/users took 4367ms
info: Aug-01-2025 22:54:13: 	Database health check passed (35ms)
error: Aug-01-2025 22:54:13: 	FAILED REQUEST [6sdq0u]: POST /api/adtipcall returned 400
error: Aug-01-2025 22:54:37: 	FAILED REQUEST [w9s1h]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 22:54:40: 	SLOW REQUEST [rz3g7p]: POST /api/users took 3474ms
info: Aug-01-2025 22:54:43: 	Connection pool stats:
info: Aug-01-2025 22:54:43: 	Database health check passed (51ms)
error: Aug-01-2025 22:54:50: 	FAILED REQUEST [537as]: POST /api/adtipcall returned 400
warn: Aug-01-2025 22:55:12: 	SLOW REQUEST [z6r7zb]: POST /api/updateuser took 2082ms
info: Aug-01-2025 22:55:13: 	Database health check passed (49ms)
warn: Aug-01-2025 22:55:37: 	SLOW REQUEST [o8ux2z]: POST /api/adtipcall took 3034ms
info: Aug-01-2025 22:55:43: 	Connection pool stats:
info: Aug-01-2025 22:55:43: 	Database health check passed (37ms)
warn: Aug-01-2025 22:55:55: 	SLOW REQUEST [dtspr]: POST /api/voice-call took 2723ms
warn: Aug-01-2025 22:55:57: 	SLOW REQUEST [mpyoki]: POST /api/voice-call took 3037ms
error: Aug-01-2025 22:56:01: 	FAILED REQUEST [iab69j]: GET /api/get-fcm-token/unknown returned 404
info: Aug-01-2025 22:56:14: 	Database health check passed (1169ms)
info: Aug-01-2025 22:56:43: 	Connection pool stats:
info: Aug-01-2025 22:56:43: 	Database health check passed (38ms)
warn: Aug-01-2025 22:57:12: 	SLOW REQUEST [icniy6]: GET /api/getvideos/58422/0/1 took 6990ms
info: Aug-01-2025 22:57:13: 	Database health check passed (40ms)
warn: Aug-01-2025 22:57:25: 	SLOW REQUEST [1etvei]: GET /api/getvideos/58422/0/1 took 5416ms
warn: Aug-01-2025 22:57:25: 	SLOW REQUEST [14bfw]: GET /api/getvideos/58422/0/1 took 3387ms
info: Aug-01-2025 22:57:43: 	Connection pool stats:
info: Aug-01-2025 22:57:44: 	Database health check passed (447ms)
warn: Aug-01-2025 22:57:50: 	SLOW REQUEST [vfhcxd]: GET /api/getvideos/58422/0/1 took 4523ms
info: Aug-01-2025 22:58:13: 	Database health check passed (51ms)
warn: Aug-01-2025 22:58:23: 	SLOW REQUEST [8049f]: GET /api/getvideos/58422/0/1 took 7065ms
info: Aug-01-2025 22:58:43: 	Connection pool stats:
info: Aug-01-2025 22:58:44: 	Database health check passed (353ms)
info: Aug-01-2025 22:59:14: 	Database health check passed (362ms)
error: Aug-01-2025 22:59:26: 	FAILED REQUEST [h9mx3o]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 22:59:31: 	SLOW REQUEST [w8ssos]: POST /api/users took 4898ms
info: Aug-01-2025 22:59:43: 	Connection pool stats:
info: Aug-01-2025 22:59:44: 	Database health check passed (377ms)
info: Aug-01-2025 23:00:15: 	Database health check passed (1447ms)
info: Aug-01-2025 23:00:43: 	Connection pool stats:
info: Aug-01-2025 23:00:43: 	Database health check passed (193ms)
info: Aug-01-2025 23:01:13: 	Database health check passed (47ms)
info: Aug-01-2025 23:01:43: 	Connection pool stats:
info: Aug-01-2025 23:01:44: 	Database health check passed (298ms)
info: Aug-01-2025 23:02:14: 	Database health check passed (1023ms)
info: Aug-01-2025 23:02:43: 	Connection pool stats:
info: Aug-01-2025 23:02:44: 	Database health check passed (324ms)
info: Aug-01-2025 23:03:14: 	Database health check passed (949ms)
info: Aug-01-2025 23:03:43: 	Connection pool stats:
info: Aug-01-2025 23:03:44: 	Database health check passed (403ms)
info: Aug-01-2025 23:04:14: 	Database health check passed (792ms)
info: Aug-01-2025 23:04:43: 	Connection pool stats:
info: Aug-01-2025 23:04:44: 	Database health check passed (361ms)
info: Aug-01-2025 23:05:14: 	Database health check passed (544ms)
warn: Aug-01-2025 23:05:39: 	SLOW REQUEST [qmvmkt]: GET /api/ping took 2021ms
info: Aug-01-2025 23:05:43: 	Connection pool stats:
info: Aug-01-2025 23:05:44: 	Database health check passed (363ms)
error: Aug-01-2025 23:06:10: 	FAILED REQUEST [0wadr]: GET /api/check-premium/58422 returned 400
info: Aug-01-2025 23:06:14: 	Database health check passed (489ms)
info: Aug-01-2025 23:06:43: 	Connection pool stats:
info: Aug-01-2025 23:06:44: 	Database health check passed (330ms)
warn: Aug-01-2025 23:07:02: 	SLOW REQUEST [99w7wl]: GET /api/categories took 2106ms
warn: Aug-01-2025 23:07:03: 	SLOW REQUEST [ny5a0b]: GET /api/subscriptions/status/63779 took 2502ms
warn: Aug-01-2025 23:07:03: 	SLOW REQUEST [7h319g]: POST /api/list-posts took 2594ms
info: Aug-01-2025 23:07:14: 	Database health check passed (922ms)
info: Aug-01-2025 23:07:43: 	Connection pool stats:
info: Aug-01-2025 23:07:44: 	Database health check passed (375ms)
warn: Aug-01-2025 23:08:03: 	SLOW REQUEST [abk618]: GET /api/content-premium/status/58422 took 2884ms
warn: Aug-01-2025 23:08:03: 	SLOW REQUEST [1ltfhb]: GET /api/ping took 2883ms
warn: Aug-01-2025 23:08:03: 	SLOW REQUEST [qtnbfs]: POST /api/get-user-data took 2547ms
warn: Aug-01-2025 23:08:03: 	SLOW REQUEST [0n135o]: GET /api/ping took 2954ms
warn: Aug-01-2025 23:08:03: 	SLOW REQUEST [ecj6ir]: GET /api/getfunds/58422 took 3371ms
warn: Aug-01-2025 23:08:04: 	SLOW REQUEST [qixuka]: GET /api/categories took 2617ms
warn: Aug-01-2025 23:08:04: 	SLOW REQUEST [ah34z7]: GET /api/getfunds/63779 took 3109ms
info: Aug-01-2025 23:08:15: 	Database health check passed (1414ms)
error: Aug-01-2025 23:08:19: 	FAILED REQUEST [zrj65h]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 23:08:22: 	SLOW REQUEST [e7hwpr]: POST /api/users took 3600ms
info: Aug-01-2025 23:08:43: 	Connection pool stats:
info: Aug-01-2025 23:08:43: 	Database health check passed (37ms)
info: Aug-01-2025 23:09:13: 	Database health check passed (44ms)
info: Aug-01-2025 23:09:43: 	Connection pool stats:
info: Aug-01-2025 23:09:43: 	Database health check passed (40ms)
info: Aug-01-2025 23:10:13: 	Database health check passed (66ms)
info: Aug-01-2025 23:10:43: 	Connection pool stats:
info: Aug-01-2025 23:10:43: 	Database health check passed (36ms)
info: Aug-01-2025 23:11:13: 	Database health check passed (87ms)
info: Aug-01-2025 23:11:43: 	Connection pool stats:
info: Aug-01-2025 23:11:44: 	Database health check passed (263ms)
error: Aug-01-2025 23:11:52: 	FAILED REQUEST [t8apmw]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 23:11:56: 	SLOW REQUEST [60pxl]: POST /api/users took 4585ms
warn: Aug-01-2025 23:12:12: 	SLOW REQUEST [ruf1f7]: POST /api/adtipcall took 4325ms
info: Aug-01-2025 23:12:13: 	Database health check passed (49ms)
error: Aug-01-2025 23:12:25: 	FAILED REQUEST [hiw27]: GET /api/get-fcm-token/unknown returned 404
warn: Aug-01-2025 23:12:35: 	SLOW REQUEST [vobykb]: POST /api/adtipcall took 3636ms
info: Aug-01-2025 23:12:43: 	Connection pool stats:
info: Aug-01-2025 23:12:44: 	Database health check passed (369ms)
warn: Aug-01-2025 23:12:48: 	SLOW REQUEST [88bv0a]: POST /api/voice-call took 4154ms
info: Aug-01-2025 23:13:14: 	Database health check passed (383ms)
info: Aug-01-2025 23:13:43: 	Connection pool stats:
info: Aug-01-2025 23:13:44: 	Database health check passed (286ms)
error: Aug-01-2025 23:13:49: 	FAILED REQUEST [vtlk9w]: GET /api/get-fcm-token/unknown returned 404
info: Aug-01-2025 23:14:14: 	Database health check passed (227ms)
info: Aug-01-2025 23:14:43: 	Connection pool stats:
info: Aug-01-2025 23:14:44: 	Database health check passed (77ms)
info: Aug-01-2025 23:15:14: 	Database health check passed (177ms)
info: Aug-01-2025 23:15:43: 	Connection pool stats:
info: Aug-01-2025 23:15:44: 	Database health check passed (335ms)
info: Aug-01-2025 23:16:14: 	Database health check passed (48ms)
info: Aug-01-2025 23:16:43: 	Connection pool stats:
info: Aug-01-2025 23:16:44: 	Database health check passed (51ms)
info: Aug-01-2025 23:17:14: 	Database health check passed (40ms)
info: Aug-01-2025 23:17:43: 	Connection pool stats:
info: Aug-01-2025 23:17:44: 	Database health check passed (38ms)
info: Aug-01-2025 23:18:14: 	Database health check passed (44ms)
info: Aug-01-2025 23:18:43: 	Connection pool stats:
info: Aug-01-2025 23:18:44: 	Database health check passed (50ms)
info: Aug-01-2025 23:19:14: 	Database health check passed (169ms)
info: Aug-01-2025 23:19:43: 	Connection pool stats:
info: Aug-01-2025 23:19:44: 	Database health check passed (250ms)
info: Aug-01-2025 23:20:14: 	Database health check passed (548ms)
error: Aug-01-2025 23:20:14: 	FAILED REQUEST [t6u5a]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 23:20:19: 	SLOW REQUEST [hvgkjjb]: POST /api/users took 5239ms
warn: Aug-01-2025 23:20:29: 	SLOW REQUEST [ksg3uh]: POST /api/adtipcall took 4401ms
warn: Aug-01-2025 23:20:40: 	SLOW REQUEST [ccw4z9]: POST /api/voice-call took 3170ms
info: Aug-01-2025 23:20:43: 	Connection pool stats:
info: Aug-01-2025 23:20:44: 	Database health check passed (156ms)
error: Aug-01-2025 23:20:44: 	FAILED REQUEST [bhxnd]: GET /api/get-fcm-token/unknown returned 404
warn: Aug-01-2025 23:20:51: 	SLOW REQUEST [mvrl29]: POST /api/adtipcall took 2977ms
warn: Aug-01-2025 23:21:08: 	SLOW REQUEST [n8la5b]: POST /api/voice-call took 3049ms
info: Aug-01-2025 23:21:14: 	Database health check passed (301ms)
info: Aug-01-2025 23:21:43: 	Connection pool stats:
info: Aug-01-2025 23:21:44: 	Database health check passed (480ms)
info: Aug-01-2025 23:22:14: 	Database health check passed (405ms)
error: Aug-01-2025 23:22:17: 	FAILED REQUEST [pdjz4p]: GET /api/get-fcm-token/unknown returned 404
info: Aug-01-2025 23:22:43: 	Connection pool stats:
info: Aug-01-2025 23:22:44: 	Database health check passed (67ms)
info: Aug-01-2025 23:23:14: 	Database health check passed (48ms)
info: Aug-01-2025 23:23:43: 	Connection pool stats:
info: Aug-01-2025 23:23:44: 	Database health check passed (43ms)
info: Aug-01-2025 23:24:14: 	Database health check passed (36ms)
info: Aug-01-2025 23:24:43: 	Connection pool stats:
info: Aug-01-2025 23:24:44: 	Database health check passed (48ms)
info: Aug-01-2025 23:25:14: 	Database health check passed (44ms)
info: Aug-01-2025 23:25:43: 	Connection pool stats:
info: Aug-01-2025 23:25:44: 	Database health check passed (48ms)
info: Aug-01-2025 23:26:14: 	Database health check passed (87ms)
info: Aug-01-2025 23:26:43: 	Connection pool stats:
info: Aug-01-2025 23:26:44: 	Database health check passed (48ms)
info: Aug-01-2025 23:27:14: 	Database health check passed (52ms)
info: Aug-01-2025 23:27:43: 	Connection pool stats:
info: Aug-01-2025 23:27:44: 	Database health check passed (40ms)
info: Aug-01-2025 23:28:14: 	Database health check passed (37ms)
info: Aug-01-2025 23:28:43: 	Connection pool stats:
info: Aug-01-2025 23:28:44: 	Database health check passed (60ms)
info: Aug-01-2025 23:29:14: 	Database health check passed (40ms)
info: Aug-01-2025 23:29:43: 	Connection pool stats:
info: Aug-01-2025 23:29:44: 	Database health check passed (391ms)
warn: Aug-01-2025 23:29:58: 	SLOW REQUEST [f4xhwm]: POST /api/list-posts took 3831ms
error: Aug-01-2025 23:30:03: 	FAILED REQUEST [uk4o6]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 23:30:06: 	SLOW REQUEST [bgp77r]: POST /api/users took 3548ms
info: Aug-01-2025 23:30:14: 	Database health check passed (50ms)
warn: Aug-01-2025 23:30:21: 	SLOW REQUEST [u1h0qm]: POST /api/voice-call took 3468ms
warn: Aug-01-2025 23:30:25: 	SLOW REQUEST [9gvcrd]: POST /api/voice-call took 3988ms
info: Aug-01-2025 23:30:43: 	Connection pool stats:
info: Aug-01-2025 23:30:44: 	Database health check passed (397ms)
warn: Aug-01-2025 23:30:45: 	SLOW REQUEST [8xyprp]: POST /api/adtipcall took 2792ms
warn: Aug-01-2025 23:31:08: 	SLOW REQUEST [kxxzvy]: POST /api/voice-call took 3606ms
info: Aug-01-2025 23:31:14: 	Database health check passed (319ms)
info: Aug-01-2025 23:31:43: 	Connection pool stats:
info: Aug-01-2025 23:31:44: 	Database health check passed (51ms)
info: Aug-01-2025 23:32:14: 	Database health check passed (104ms)
info: Aug-01-2025 23:32:43: 	Connection pool stats:
info: Aug-01-2025 23:32:44: 	Database health check passed (42ms)
warn: Aug-01-2025 23:32:54: 	SLOW REQUEST [u1w7w9]: GET /api/categories took 2126ms
warn: Aug-01-2025 23:32:54: 	SLOW REQUEST [4pwga]: GET /api/subscriptions/status/63779 took 2129ms
warn: Aug-01-2025 23:32:54: 	SLOW REQUEST [0bgrie]: GET /api/getfunds/58422 took 2584ms
warn: Aug-01-2025 23:32:55: 	SLOW REQUEST [m766k]: GET /api/categories took 2466ms
warn: Aug-01-2025 23:32:55: 	SLOW REQUEST [vf7fil]: GET /api/subscriptions/status/58422 took 2461ms
warn: Aug-01-2025 23:32:55: 	SLOW REQUEST [91e1w]: POST /api/list-posts took 2972ms
warn: Aug-01-2025 23:32:55: 	SLOW REQUEST [zzh6xj]: POST /api/list-posts took 2928ms
info: Aug-01-2025 23:33:14: 	Database health check passed (222ms)
warn: Aug-01-2025 23:33:24: 	SLOW REQUEST [vl65m]: GET /api/getfunds/63779 took 2187ms
warn: Aug-01-2025 23:33:29: 	SLOW REQUEST [j9d6np]: GET /api/subscriptions/status/58422 took 2130ms
info: Aug-01-2025 23:33:43: 	Connection pool stats:
info: Aug-01-2025 23:33:44: 	Database health check passed (93ms)
info: Aug-01-2025 23:34:14: 	Database health check passed (94ms)
info: Aug-01-2025 23:34:43: 	Connection pool stats:
info: Aug-01-2025 23:34:44: 	Database health check passed (50ms)
info: Aug-01-2025 23:35:14: 	Database health check passed (49ms)
error: Aug-01-2025 23:35:19: 	FAILED REQUEST [3f17h6]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 23:35:22: 	SLOW REQUEST [e48cy8]: POST /api/users took 3209ms
info: Aug-01-2025 23:35:43: 	Connection pool stats:
info: Aug-01-2025 23:35:45: 	Database health check passed (987ms)
info: Aug-01-2025 23:36:15: 	Database health check passed (982ms)
warn: Aug-01-2025 23:36:16: 	SLOW REQUEST [hbnbj8]: POST /api/adtipcall took 4042ms
info: Aug-01-2025 23:36:43: 	Connection pool stats:
info: Aug-01-2025 23:36:44: 	Database health check passed (231ms)
info: Aug-01-2025 23:37:14: 	Database health check passed (122ms)
info: Aug-01-2025 23:37:43: 	Connection pool stats:
info: Aug-01-2025 23:37:44: 	Database health check passed (45ms)
info: Aug-01-2025 23:38:14: 	Database health check passed (298ms)
info: Aug-01-2025 23:38:43: 	Connection pool stats:
info: Aug-01-2025 23:38:44: 	Database health check passed (397ms)
info: Aug-01-2025 23:39:18: 	Database health check passed (4327ms)
info: Aug-01-2025 23:39:43: 	Connection pool stats:
info: Aug-01-2025 23:39:44: 	Database health check passed (73ms)
info: Aug-01-2025 23:40:14: 	Database health check passed (37ms)
info: Aug-01-2025 23:40:43: 	Connection pool stats:
info: Aug-01-2025 23:40:44: 	Database health check passed (49ms)
info: Aug-01-2025 23:41:14: 	Database health check passed (72ms)
info: Aug-01-2025 23:41:43: 	Connection pool stats:
info: Aug-01-2025 23:41:44: 	Database health check passed (38ms)
info: Aug-01-2025 23:42:14: 	Database health check passed (43ms)
info: Aug-01-2025 23:42:43: 	Connection pool stats:
info: Aug-01-2025 23:42:44: 	Database health check passed (49ms)
info: Aug-01-2025 23:43:14: 	Database health check passed (59ms)
info: Aug-01-2025 23:43:44: 	Connection pool stats:
info: Aug-01-2025 23:43:44: 	Database health check passed (37ms)
error: Aug-01-2025 23:44:00: 	FAILED REQUEST [xga9x]: GET /api/check-premium/58422 returned 400
warn: Aug-01-2025 23:44:03: 	SLOW REQUEST [zhqo5]: POST /api/users took 3305ms
info: Aug-01-2025 23:44:15: 	Database health check passed (639ms)
warn: Aug-01-2025 23:44:37: 	SLOW REQUEST [4s6vi6]: GET /api/check-premium/63779 took 3920ms
error: Aug-01-2025 23:44:37: 	FAILED REQUEST [4s6vi6]: GET /api/check-premium/63779 returned 400
warn: Aug-01-2025 23:44:39: 	SLOW REQUEST [xy948h]: GET /api/missed-calls/63779 took 6019ms
warn: Aug-01-2025 23:44:42: 	SLOW REQUEST [5dia4s]: POST /api/users took 9462ms
info: Aug-01-2025 23:44:44: 	Connection pool stats:
info: Aug-01-2025 23:44:46: 	Database health check passed (1841ms)
warn: Aug-01-2025 23:44:48: 	SLOW REQUEST [42n84mf]: POST /api/voice-call took 23698ms
warn: Aug-01-2025 23:44:49: 	SLOW REQUEST [vk0sve]: POST /api/voice-call took 12550ms
info: Aug-01-2025 23:45:14: 	Database health check passed (54ms)
info: Aug-01-2025 23:45:44: 	Connection pool stats:
info: Aug-01-2025 23:45:44: 	Database health check passed (53ms)
info: Aug-01-2025 23:46:14: 	Database health check passed (37ms)
info: Aug-01-2025 23:46:44: 	Connection pool stats:
info: Aug-01-2025 23:46:44: 	Database health check passed (38ms)
info: Aug-01-2025 23:47:14: 	Database health check passed (42ms)
info: Aug-01-2025 23:47:44: 	Connection pool stats:
info: Aug-01-2025 23:47:44: 	Database health check passed (44ms)
info: Aug-01-2025 23:48:14: 	Database health check passed (266ms)
info: Aug-01-2025 23:48:44: 	Connection pool stats:
info: Aug-01-2025 23:48:44: 	Database health check passed (44ms)
info: Aug-01-2025 23:49:14: 	Database health check passed (42ms)
info: Aug-01-2025 23:49:44: 	Connection pool stats:
info: Aug-01-2025 23:49:44: 	Database health check passed (40ms)
info: Aug-01-2025 23:50:14: 	Database health check passed (56ms)
warn: Aug-01-2025 23:50:36: 	SLOW REQUEST [08289q]: GET /api/list-premium-posts took 4760ms
error: Aug-01-2025 23:50:42: 	FAILED REQUEST [jsw1nz]: GET /api/check-premium/58422 returned 400
info: Aug-01-2025 23:50:44: 	Connection pool stats:
info: Aug-01-2025 23:50:44: 	Database health check passed (44ms)
warn: Aug-01-2025 23:50:45: 	SLOW REQUEST [8aqw7g]: POST /api/users took 3265ms
error: Aug-01-2025 23:51:03: 	FAILED REQUEST [hbwosu]: GET /api/check-premium/63779 returned 400
warn: Aug-01-2025 23:51:06: 	SLOW REQUEST [r1ttdb]: POST /api/users took 3296ms
info: Aug-01-2025 23:51:14: 	Database health check passed (48ms)
info: Aug-01-2025 23:51:44: 	Connection pool stats:
info: Aug-01-2025 23:51:44: 	Database health check passed (52ms)
info: Aug-01-2025 23:52:14: 	Database health check passed (46ms)
info: Aug-01-2025 23:52:44: 	Connection pool stats:
info: Aug-01-2025 23:52:44: 	Database health check passed (51ms)
info: Aug-01-2025 23:53:14: 	Database health check passed (99ms)
info: Aug-01-2025 23:53:44: 	Connection pool stats:
info: Aug-01-2025 23:53:44: 	Database health check passed (53ms)
info: Aug-01-2025 23:54:14: 	Database health check passed (37ms)
info: Aug-01-2025 23:54:44: 	Connection pool stats:
info: Aug-01-2025 23:54:44: 	Database health check passed (54ms)
info: Aug-01-2025 23:55:14: 	Database health check passed (44ms)
info: Aug-01-2025 23:55:44: 	Connection pool stats:
info: Aug-01-2025 23:55:44: 	Database health check passed (63ms)
info: Aug-01-2025 23:56:14: 	Database health check passed (48ms)
info: Aug-01-2025 23:56:44: 	Connection pool stats:
info: Aug-01-2025 23:56:44: 	Database health check passed (68ms)
info: Aug-01-2025 23:57:15: 	Database health check passed (518ms)
info: Aug-01-2025 23:57:44: 	Connection pool stats:
info: Aug-01-2025 23:57:44: 	Database health check passed (51ms)
info: Aug-01-2025 23:58:14: 	Database health check passed (36ms)
info: Aug-01-2025 23:58:44: 	Connection pool stats:
info: Aug-01-2025 23:58:44: 	Database health check passed (42ms)
info: Aug-01-2025 23:59:14: 	Database health check passed (53ms)
info: Aug-01-2025 23:59:44: 	Connection pool stats:
info: Aug-01-2025 23:59:44: 	Database health check passed (56ms)
info: Aug-02-2025 00:00:14: 	Database health check passed (43ms)
info: Aug-02-2025 00:00:44: 	Connection pool stats:
info: Aug-02-2025 00:00:44: 	Database health check passed (50ms)
info: Aug-02-2025 00:01:14: 	Database health check passed (44ms)
info: Aug-02-2025 00:01:44: 	Connection pool stats:
info: Aug-02-2025 00:01:44: 	Database health check passed (66ms)
info: Aug-02-2025 00:02:14: 	Database health check passed (48ms)
info: Aug-02-2025 00:02:44: 	Connection pool stats:
info: Aug-02-2025 00:02:44: 	Database health check passed (54ms)
info: Aug-02-2025 00:03:14: 	Database health check passed (41ms)
info: Aug-02-2025 00:03:44: 	Connection pool stats:
info: Aug-02-2025 00:03:44: 	Database health check passed (58ms)
info: Aug-02-2025 00:04:27: 	Performance monitoring started
info: Aug-02-2025 00:04:35: 	Starting database health monitoring
info: Aug-02-2025 00:04:35: 	Database health check passed (267ms)
info: Aug-02-2025 00:05:05: 	Database health check passed (48ms)
info: Aug-02-2025 00:05:35: 	Connection pool stats:
info: Aug-02-2025 00:05:35: 	Database health check passed (51ms)
info: Aug-02-2025 00:05:58: 	Performance monitoring started
info: Aug-02-2025 00:06:00: 	Starting database health monitoring
info: Aug-02-2025 00:06:00: 	Database health check passed (169ms)
info: Aug-02-2025 00:06:30: 	Database health check passed (51ms)
info: Aug-02-2025 00:07:00: 	Connection pool stats:
info: Aug-02-2025 00:07:00: 	Database health check passed (40ms)
info: Aug-02-2025 00:07:30: 	Database health check passed (42ms)
info: Aug-02-2025 00:08:00: 	Connection pool stats:
info: Aug-02-2025 00:08:00: 	Database health check passed (65ms)
info: Aug-02-2025 00:08:30: 	Database health check passed (48ms)
error: Aug-02-2025 00:08:51: 	FAILED REQUEST [bsyzb3]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:08:54: 	SLOW REQUEST [ii61x8]: POST /api/users took 3195ms
info: Aug-02-2025 00:09:00: 	Connection pool stats:
info: Aug-02-2025 00:09:00: 	Database health check passed (55ms)
info: Aug-02-2025 00:09:30: 	Database health check passed (43ms)
info: Aug-02-2025 00:10:00: 	Connection pool stats:
info: Aug-02-2025 00:10:00: 	Database health check passed (46ms)
info: Aug-02-2025 00:10:30: 	Database health check passed (62ms)
info: Aug-02-2025 00:11:00: 	Connection pool stats:
info: Aug-02-2025 00:11:00: 	Database health check passed (42ms)
info: Aug-02-2025 00:11:30: 	Database health check passed (47ms)
error: Aug-02-2025 00:11:32: 	FAILED REQUEST [7let3]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:11:35: 	SLOW REQUEST [qpeced]: POST /api/users took 3557ms
warn: Aug-02-2025 00:11:56: 	SLOW REQUEST [za7nbg]: POST /api/adtipcall took 5661ms
info: Aug-02-2025 00:12:00: 	Connection pool stats:
info: Aug-02-2025 00:12:00: 	Database health check passed (158ms)
info: Aug-02-2025 00:12:30: 	Database health check passed (44ms)
info: Aug-02-2025 00:13:00: 	Connection pool stats:
info: Aug-02-2025 00:13:00: 	Database health check passed (87ms)
info: Aug-02-2025 00:13:30: 	Database health check passed (49ms)
info: Aug-02-2025 00:14:00: 	Connection pool stats:
info: Aug-02-2025 00:14:01: 	Database health check passed (655ms)
info: Aug-02-2025 00:14:30: 	Database health check passed (39ms)
warn: Aug-02-2025 00:14:38: 	SLOW REQUEST [q2nqyi]: POST /api/voice-call took 3007ms
info: Aug-02-2025 00:15:00: 	Connection pool stats:
info: Aug-02-2025 00:15:00: 	Database health check passed (54ms)
info: Aug-02-2025 00:15:30: 	Database health check passed (64ms)
info: Aug-02-2025 00:16:00: 	Connection pool stats:
info: Aug-02-2025 00:16:01: 	Database health check passed (269ms)
info: Aug-02-2025 00:16:30: 	Database health check passed (64ms)
info: Aug-02-2025 00:17:00: 	Connection pool stats:
info: Aug-02-2025 00:17:01: 	Database health check passed (414ms)
info: Aug-02-2025 00:17:30: 	Database health check passed (54ms)
info: Aug-02-2025 00:18:00: 	Connection pool stats:
info: Aug-02-2025 00:18:00: 	Database health check passed (59ms)
info: Aug-02-2025 00:18:30: 	Database health check passed (51ms)
info: Aug-02-2025 00:19:00: 	Connection pool stats:
info: Aug-02-2025 00:19:00: 	Database health check passed (58ms)
info: Aug-02-2025 00:19:30: 	Database health check passed (49ms)
info: Aug-02-2025 00:20:00: 	Connection pool stats:
info: Aug-02-2025 00:20:00: 	Database health check passed (146ms)
info: Aug-02-2025 00:20:32: 	Database health check passed (1438ms)
info: Aug-02-2025 00:21:00: 	Connection pool stats:
info: Aug-02-2025 00:21:00: 	Database health check passed (54ms)
info: Aug-02-2025 00:21:33: 	Database health check passed (2425ms)
info: Aug-02-2025 00:22:00: 	Connection pool stats:
info: Aug-02-2025 00:22:00: 	Database health check passed (55ms)
info: Aug-02-2025 00:22:30: 	Database health check passed (49ms)
info: Aug-02-2025 00:23:00: 	Connection pool stats:
info: Aug-02-2025 00:23:01: 	Database health check passed (326ms)
info: Aug-02-2025 00:23:30: 	Database health check passed (93ms)
info: Aug-02-2025 00:24:00: 	Connection pool stats:
info: Aug-02-2025 00:24:00: 	Database health check passed (48ms)
info: Aug-02-2025 00:24:31: 	Database health check passed (388ms)
info: Aug-02-2025 00:25:00: 	Connection pool stats:
info: Aug-02-2025 00:25:01: 	Database health check passed (419ms)
info: Aug-02-2025 00:25:31: 	Database health check passed (416ms)
info: Aug-02-2025 00:26:00: 	Connection pool stats:
warn: Aug-02-2025 00:26:02: 	SLOW REQUEST [qhjwif]: POST /api/get-user-data took 2492ms
warn: Aug-02-2025 00:26:02: 	SLOW REQUEST [n4uhja]: GET /api/ping took 2507ms
info: Aug-02-2025 00:26:02: 	Database health check passed (1989ms)
warn: Aug-02-2025 00:26:02: 	SLOW REQUEST [qkvn5]: GET /api/categories took 2493ms
warn: Aug-02-2025 00:26:02: 	SLOW REQUEST [6z1fbo]: GET /api/getfunds/58422 took 2967ms
info: Aug-02-2025 00:26:31: 	Database health check passed (215ms)
info: Aug-02-2025 00:27:00: 	Connection pool stats:
info: Aug-02-2025 00:27:00: 	Database health check passed (56ms)
info: Aug-02-2025 00:27:30: 	Database health check passed (71ms)
info: Aug-02-2025 00:28:00: 	Connection pool stats:
info: Aug-02-2025 00:28:02: 	Database health check passed (1466ms)
info: Aug-02-2025 00:28:30: 	Database health check passed (93ms)
info: Aug-02-2025 00:29:00: 	Connection pool stats:
info: Aug-02-2025 00:29:01: 	Database health check passed (1071ms)
info: Aug-02-2025 00:29:31: 	Database health check passed (148ms)
info: Aug-02-2025 00:30:00: 	Connection pool stats:
info: Aug-02-2025 00:30:01: 	Database health check passed (725ms)
info: Aug-02-2025 00:30:31: 	Database health check passed (196ms)
info: Aug-02-2025 00:31:00: 	Connection pool stats:
info: Aug-02-2025 00:31:01: 	Database health check passed (410ms)
info: Aug-02-2025 00:31:31: 	Database health check passed (183ms)
info: Aug-02-2025 00:32:00: 	Connection pool stats:
info: Aug-02-2025 00:32:01: 	Database health check passed (545ms)
info: Aug-02-2025 00:32:31: 	Database health check passed (721ms)
info: Aug-02-2025 00:33:00: 	Connection pool stats:
info: Aug-02-2025 00:33:01: 	Database health check passed (264ms)
info: Aug-02-2025 00:33:31: 	Database health check passed (184ms)
info: Aug-02-2025 00:34:00: 	Connection pool stats:
info: Aug-02-2025 00:34:01: 	Database health check passed (412ms)
info: Aug-02-2025 00:34:31: 	Database health check passed (108ms)
info: Aug-02-2025 00:35:00: 	Connection pool stats:
info: Aug-02-2025 00:35:01: 	Database health check passed (245ms)
info: Aug-02-2025 00:35:31: 	Database health check passed (368ms)
info: Aug-02-2025 00:36:00: 	Connection pool stats:
info: Aug-02-2025 00:36:01: 	Database health check passed (59ms)
info: Aug-02-2025 00:36:31: 	Database health check passed (347ms)
info: Aug-02-2025 00:37:00: 	Connection pool stats:
info: Aug-02-2025 00:37:01: 	Database health check passed (564ms)
error: Aug-02-2025 00:37:11: 	FAILED REQUEST [k97p1d]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:37:14: 	SLOW REQUEST [3w7d79]: POST /api/users took 3583ms
info: Aug-02-2025 00:37:31: 	Database health check passed (233ms)
error: Aug-02-2025 00:37:31: 	FAILED REQUEST [t074sq]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:37:34: 	SLOW REQUEST [8ew7yq]: POST /api/users took 3500ms
warn: Aug-02-2025 00:37:49: 	SLOW REQUEST [fqt72]: POST /api/adtipcall took 6303ms
info: Aug-02-2025 00:38:00: 	Connection pool stats:
info: Aug-02-2025 00:38:01: 	Database health check passed (131ms)
info: Aug-02-2025 00:38:32: 	Database health check passed (1135ms)
warn: Aug-02-2025 00:38:35: 	SLOW REQUEST [3hvsw]: POST /api/voice-call took 5860ms
info: Aug-02-2025 00:39:00: 	Connection pool stats:
info: Aug-02-2025 00:39:01: 	Database health check passed (224ms)
info: Aug-02-2025 00:39:31: 	Database health check passed (251ms)
info: Aug-02-2025 00:40:00: 	Connection pool stats:
info: Aug-02-2025 00:40:01: 	Database health check passed (279ms)
info: Aug-02-2025 00:40:31: 	Database health check passed (915ms)
error: Aug-02-2025 00:40:38: 	FAILED REQUEST [hoostd]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:40:43: 	SLOW REQUEST [04eg6]: POST /api/users took 5435ms
warn: Aug-02-2025 00:40:55: 	SLOW REQUEST [sqbvd]: POST /api/adtipcall took 5031ms
info: Aug-02-2025 00:41:00: 	Connection pool stats:
info: Aug-02-2025 00:41:01: 	Database health check passed (309ms)
warn: Aug-02-2025 00:41:02: 	SLOW REQUEST [1omrwr]: POST /api/voice-call took 3899ms
warn: Aug-02-2025 00:41:16: 	SLOW REQUEST [5s9yhu]: POST /api/voice-call took 4025ms
info: Aug-02-2025 00:41:31: 	Database health check passed (675ms)
info: Aug-02-2025 00:42:00: 	Connection pool stats:
info: Aug-02-2025 00:42:01: 	Database health check passed (421ms)
warn: Aug-02-2025 00:42:07: 	SLOW REQUEST [n9q5xf]: GET /api/categories took 2126ms
warn: Aug-02-2025 00:42:07: 	SLOW REQUEST [35mcpi]: GET /api/getfunds/58422 took 2587ms
warn: Aug-02-2025 00:42:07: 	SLOW REQUEST [dcy6la]: POST /api/list-posts took 2556ms
info: Aug-02-2025 00:42:31: 	Database health check passed (706ms)
warn: Aug-02-2025 00:42:55: 	SLOW REQUEST [tf71yp]: POST /api/get-user-data took 2333ms
warn: Aug-02-2025 00:42:55: 	SLOW REQUEST [fapb1rp]: GET /api/content-premium/status/63779 took 2300ms
warn: Aug-02-2025 00:42:55: 	SLOW REQUEST [5hrhiq]: GET /api/ping took 2229ms
warn: Aug-02-2025 00:42:56: 	SLOW REQUEST [4h8iv]: GET /api/list-premium-posts took 2977ms
warn: Aug-02-2025 00:42:56: 	SLOW REQUEST [6lqypd]: GET /api/getfunds/63779 took 2389ms
warn: Aug-02-2025 00:42:58: 	SLOW REQUEST [rduqx]: GET /api/categories took 2864ms
warn: Aug-02-2025 00:42:58: 	SLOW REQUEST [jidr57]: GET /api/subscriptions/status/63779 took 2890ms
warn: Aug-02-2025 00:42:59: 	SLOW REQUEST [hscfeg]: POST /api/update-fcm-token took 3119ms
warn: Aug-02-2025 00:42:59: 	SLOW REQUEST [vfmzxm]: GET /api/getfunds/63779 took 3616ms
warn: Aug-02-2025 00:43:00: 	SLOW REQUEST [zmgkrk]: POST /api/list-posts took 4022ms
info: Aug-02-2025 00:43:00: 	Connection pool stats:
info: Aug-02-2025 00:43:01: 	Database health check passed (374ms)
warn: Aug-02-2025 00:43:31: 	SLOW REQUEST [nl4ryc]: GET /api/ping took 2416ms
warn: Aug-02-2025 00:43:31: 	SLOW REQUEST [puzc5i]: GET /api/getchannelbyuserid/63779 took 2016ms
info: Aug-02-2025 00:43:31: 	Database health check passed (406ms)
warn: Aug-02-2025 00:43:31: 	SLOW REQUEST [h2fuum]: GET /api/getfunds/58422 took 2474ms
warn: Aug-02-2025 00:43:31: 	SLOW REQUEST [rngi5]: GET /api/list-premium-posts took 2872ms
error: Aug-02-2025 00:43:39: 	FAILED REQUEST [i395br]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:43:43: 	SLOW REQUEST [tvjnqf]: POST /api/users took 4576ms
info: Aug-02-2025 00:44:00: 	Connection pool stats:
info: Aug-02-2025 00:44:01: 	Database health check passed (58ms)
error: Aug-02-2025 00:44:05: 	FAILED REQUEST [dtqz44]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:44:07: 	SLOW REQUEST [rencyu]: GET /api/missed-calls/58422 took 2453ms
warn: Aug-02-2025 00:44:08: 	SLOW REQUEST [tshc2w]: POST /api/users took 3585ms
warn: Aug-02-2025 00:44:19: 	SLOW REQUEST [b9ep7]: POST /api/adtipcall took 4017ms
info: Aug-02-2025 00:44:31: 	Database health check passed (439ms)
warn: Aug-02-2025 00:44:32: 	SLOW REQUEST [1y9jk8]: POST /api/voice-call took 5143ms
info: Aug-02-2025 00:45:00: 	Connection pool stats:
info: Aug-02-2025 00:45:01: 	Database health check passed (161ms)
info: Aug-02-2025 00:45:31: 	Database health check passed (314ms)
warn: Aug-02-2025 00:46:00: 	SLOW REQUEST [dnt9]: GET /api/subscriptions/status/63779 took 2103ms
info: Aug-02-2025 00:46:00: 	Connection pool stats:
info: Aug-02-2025 00:46:01: 	Database health check passed (234ms)
info: Aug-02-2025 00:46:31: 	Database health check passed (383ms)
info: Aug-02-2025 00:47:00: 	Connection pool stats:
info: Aug-02-2025 00:47:01: 	Database health check passed (44ms)
info: Aug-02-2025 00:47:32: 	Database health check passed (1271ms)
warn: Aug-02-2025 00:48:00: 	SLOW REQUEST [5qn10v]: GET /api/getfunds/58422 took 3857ms
warn: Aug-02-2025 00:48:00: 	SLOW REQUEST [igjzxm]: GET /api/categories took 3474ms
info: Aug-02-2025 00:48:00: 	Connection pool stats:
warn: Aug-02-2025 00:48:00: 	SLOW REQUEST [l001zt]: GET /api/content-premium/status/63779 took 3356ms
warn: Aug-02-2025 00:48:00: 	SLOW REQUEST [fvb905]: GET /api/subscriptions/status/58422 took 3360ms
warn: Aug-02-2025 00:48:01: 	SLOW REQUEST [wxepqi]: GET /api/list-premium-posts took 3463ms
warn: Aug-02-2025 00:48:01: 	SLOW REQUEST [cpu7jk]: POST /api/list-posts took 3482ms
error: Aug-02-2025 00:48:01: 	FAILED REQUEST [pafdcl]: GET /api/check-premium/58422 returned 400
info: Aug-02-2025 00:48:01: 	Database health check passed (54ms)
warn: Aug-02-2025 00:48:04: 	SLOW REQUEST [jrpkh]: POST /api/users took 3503ms
info: Aug-02-2025 00:48:31: 	Database health check passed (51ms)
error: Aug-02-2025 00:48:40: 	FAILED REQUEST [nq24h]: GET /api/get-fcm-token/unknown returned 404
info: Aug-02-2025 00:49:00: 	Connection pool stats:
info: Aug-02-2025 00:49:01: 	Database health check passed (348ms)
warn: Aug-02-2025 00:49:03: 	SLOW REQUEST [47cl7e]: POST /api/voice-call took 3443ms
error: Aug-02-2025 00:49:05: 	FAILED REQUEST [sk0jsu]: GET /api/get-fcm-token/unknown returned 404
info: Aug-02-2025 00:49:31: 	Database health check passed (271ms)
warn: Aug-02-2025 00:49:49: 	SLOW REQUEST [dgu9hh]: GET /api/getfunds/63779 took 3003ms
warn: Aug-02-2025 00:49:49: 	SLOW REQUEST [6g4wdc]: GET /api/getfunds/63779 took 3061ms
warn: Aug-02-2025 00:49:51: 	SLOW REQUEST [rcnqtm]: POST /api/list-posts took 2120ms
info: Aug-02-2025 00:50:00: 	Connection pool stats:
info: Aug-02-2025 00:50:02: 	Database health check passed (1223ms)
warn: Aug-02-2025 00:50:02: 	SLOW REQUEST [vizwug]: POST /api/logout took 5251ms
error: Aug-02-2025 00:50:06: 	FAILED REQUEST [6h9zrq]: POST /api/logout returned 400
info: Aug-02-2025 00:50:31: 	Database health check passed (451ms)
info: Aug-02-2025 00:51:00: 	Connection pool stats:
info: Aug-02-2025 00:51:01: 	Database health check passed (49ms)
info: Aug-02-2025 00:51:31: 	Database health check passed (79ms)
info: Aug-02-2025 00:52:00: 	Connection pool stats:
info: Aug-02-2025 00:52:01: 	Database health check passed (53ms)
error: Aug-02-2025 00:52:14: 	FAILED REQUEST [h54op]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:52:17: 	SLOW REQUEST [nc7ooh]: POST /api/users took 3170ms
info: Aug-02-2025 00:52:31: 	Database health check passed (51ms)
info: Aug-02-2025 00:53:00: 	Connection pool stats:
info: Aug-02-2025 00:53:01: 	Database health check passed (50ms)
info: Aug-02-2025 00:53:31: 	Database health check passed (69ms)
info: Aug-02-2025 00:54:00: 	Connection pool stats:
info: Aug-02-2025 00:54:01: 	Database health check passed (57ms)
info: Aug-02-2025 00:54:31: 	Database health check passed (52ms)
info: Aug-02-2025 00:55:00: 	Connection pool stats:
info: Aug-02-2025 00:55:01: 	Database health check passed (55ms)
info: Aug-02-2025 00:55:31: 	Database health check passed (65ms)
info: Aug-02-2025 00:56:00: 	Connection pool stats:
info: Aug-02-2025 00:56:01: 	Database health check passed (52ms)
info: Aug-02-2025 00:56:31: 	Database health check passed (50ms)
info: Aug-02-2025 00:57:00: 	Connection pool stats:
info: Aug-02-2025 00:57:01: 	Database health check passed (59ms)
error: Aug-02-2025 00:57:20: 	FAILED REQUEST [otsgf7]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 00:57:23: 	SLOW REQUEST [h67d4e]: POST /api/users took 3182ms
info: Aug-02-2025 00:57:34: 	Database health check passed (3067ms)
info: Aug-02-2025 00:58:00: 	Connection pool stats:
info: Aug-02-2025 00:58:01: 	Database health check passed (57ms)
info: Aug-02-2025 00:58:31: 	Database health check passed (52ms)
info: Aug-02-2025 00:59:00: 	Connection pool stats:
info: Aug-02-2025 00:59:01: 	Database health check passed (54ms)
info: Aug-02-2025 00:59:31: 	Database health check passed (54ms)
info: Aug-02-2025 01:00:00: 	Connection pool stats:
info: Aug-02-2025 01:00:01: 	Database health check passed (246ms)
info: Aug-02-2025 01:00:31: 	Database health check passed (48ms)
info: Aug-02-2025 01:01:01: 	Connection pool stats:
info: Aug-02-2025 01:01:01: 	Database health check passed (52ms)
info: Aug-02-2025 01:01:31: 	Database health check passed (55ms)
info: Aug-02-2025 01:02:01: 	Connection pool stats:
info: Aug-02-2025 01:02:01: 	Database health check passed (52ms)
error: Aug-02-2025 01:02:14: 	FAILED REQUEST [twek4w]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:02:17: 	SLOW REQUEST [31y05]: POST /api/users took 3211ms
info: Aug-02-2025 01:02:31: 	Database health check passed (51ms)
info: Aug-02-2025 01:03:01: 	Connection pool stats:
info: Aug-02-2025 01:03:01: 	Database health check passed (56ms)
info: Aug-02-2025 01:03:33: 	Database health check passed (2176ms)
info: Aug-02-2025 01:04:01: 	Connection pool stats:
info: Aug-02-2025 01:04:01: 	Database health check passed (55ms)
info: Aug-02-2025 01:04:33: 	Database health check passed (2377ms)
info: Aug-02-2025 01:05:01: 	Connection pool stats:
info: Aug-02-2025 01:05:02: 	Database health check passed (945ms)
info: Aug-02-2025 01:05:31: 	Database health check passed (428ms)
info: Aug-02-2025 01:06:01: 	Connection pool stats:
info: Aug-02-2025 01:06:01: 	Database health check passed (74ms)
info: Aug-02-2025 01:06:31: 	Database health check passed (189ms)
info: Aug-02-2025 01:07:01: 	Connection pool stats:
info: Aug-02-2025 01:07:04: 	Database health check passed (2757ms)
info: Aug-02-2025 01:07:31: 	Database health check passed (90ms)
info: Aug-02-2025 01:08:01: 	Connection pool stats:
info: Aug-02-2025 01:08:01: 	Database health check passed (380ms)
info: Aug-02-2025 01:08:31: 	Database health check passed (421ms)
info: Aug-02-2025 01:09:01: 	Connection pool stats:
info: Aug-02-2025 01:09:01: 	Database health check passed (415ms)
info: Aug-02-2025 01:09:31: 	Database health check passed (267ms)
info: Aug-02-2025 01:10:01: 	Connection pool stats:
info: Aug-02-2025 01:10:03: 	Database health check passed (1869ms)
info: Aug-02-2025 01:10:31: 	Database health check passed (185ms)
info: Aug-02-2025 01:11:01: 	Connection pool stats:
info: Aug-02-2025 01:11:01: 	Database health check passed (66ms)
info: Aug-02-2025 01:11:31: 	Database health check passed (91ms)
info: Aug-02-2025 01:12:01: 	Connection pool stats:
info: Aug-02-2025 01:12:01: 	Database health check passed (79ms)
info: Aug-02-2025 01:12:31: 	Database health check passed (57ms)
error: Aug-02-2025 01:12:56: 	FAILED REQUEST [b6hxhm]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:12:59: 	SLOW REQUEST [9hoif]: POST /api/users took 3153ms
info: Aug-02-2025 01:13:01: 	Connection pool stats:
info: Aug-02-2025 01:13:01: 	Database health check passed (58ms)
info: Aug-02-2025 01:13:31: 	Database health check passed (221ms)
info: Aug-02-2025 01:14:01: 	Connection pool stats:
info: Aug-02-2025 01:14:01: 	Database health check passed (406ms)
info: Aug-02-2025 01:14:31: 	Database health check passed (51ms)
warn: Aug-02-2025 01:14:42: 	SLOW REQUEST [ycnme]: POST /api/list-posts took 2917ms
warn: Aug-02-2025 01:14:42: 	SLOW REQUEST [cxsmu7]: GET /api/getfunds/58422 took 2896ms
error: Aug-02-2025 01:14:44: 	FAILED REQUEST [k4sdw]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:14:47: 	SLOW REQUEST [osnla9]: POST /api/users took 3983ms
info: Aug-02-2025 01:15:01: 	Connection pool stats:
info: Aug-02-2025 01:15:01: 	Database health check passed (51ms)
info: Aug-02-2025 01:15:31: 	Database health check passed (446ms)
warn: Aug-02-2025 01:15:51: 	SLOW REQUEST [v5pl8f]: POST /api/voice-call took 5455ms
info: Aug-02-2025 01:16:01: 	Connection pool stats:
info: Aug-02-2025 01:16:03: 	Database health check passed (1941ms)
info: Aug-02-2025 01:16:31: 	Database health check passed (367ms)
info: Aug-02-2025 01:17:01: 	Connection pool stats:
info: Aug-02-2025 01:17:01: 	Database health check passed (78ms)
info: Aug-02-2025 01:17:31: 	Database health check passed (420ms)
info: Aug-02-2025 01:18:01: 	Connection pool stats:
info: Aug-02-2025 01:18:02: 	Database health check passed (1048ms)
info: Aug-02-2025 01:18:31: 	Database health check passed (47ms)
info: Aug-02-2025 01:19:01: 	Connection pool stats:
info: Aug-02-2025 01:19:01: 	Database health check passed (140ms)
info: Aug-02-2025 01:19:31: 	Database health check passed (112ms)
warn: Aug-02-2025 01:19:41: 	SLOW REQUEST [8596rd]: POST /api/check-app-version took 2558ms
warn: Aug-02-2025 01:19:58: 	SLOW REQUEST [39ilg]: POST /api/update-fcm-token took 2513ms
info: Aug-02-2025 01:20:01: 	Connection pool stats:
info: Aug-02-2025 01:20:01: 	Database health check passed (102ms)
info: Aug-02-2025 01:20:31: 	Database health check passed (369ms)
warn: Aug-02-2025 01:20:36: 	SLOW REQUEST [fuiamd]: POST /api/list-posts took 2903ms
warn: Aug-02-2025 01:20:36: 	SLOW REQUEST [8mdpyl]: POST /api/update-fcm-token took 2134ms
warn: Aug-02-2025 01:20:36: 	SLOW REQUEST [03o9h]: GET /api/getfunds/58422 took 2933ms
error: Aug-02-2025 01:20:53: 	FAILED REQUEST [gjiheg]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:20:56: 	SLOW REQUEST [732y2]: POST /api/users took 4045ms
info: Aug-02-2025 01:21:01: 	Connection pool stats:
info: Aug-02-2025 01:21:01: 	Database health check passed (80ms)
warn: Aug-02-2025 01:21:11: 	SLOW REQUEST [xhpmga]: POST /api/adtipcall took 5203ms
warn: Aug-02-2025 01:21:21: 	SLOW REQUEST [v4cgk]: POST /api/voice-call took 4442ms
info: Aug-02-2025 01:21:31: 	Database health check passed (231ms)
info: Aug-02-2025 01:22:01: 	Connection pool stats:
info: Aug-02-2025 01:22:01: 	Database health check passed (57ms)
info: Aug-02-2025 01:22:31: 	Database health check passed (64ms)
info: Aug-02-2025 01:23:01: 	Connection pool stats:
info: Aug-02-2025 01:23:01: 	Database health check passed (55ms)
info: Aug-02-2025 01:23:31: 	Database health check passed (53ms)
info: Aug-02-2025 01:24:01: 	Connection pool stats:
info: Aug-02-2025 01:24:01: 	Database health check passed (77ms)
info: Aug-02-2025 01:24:31: 	Database health check passed (92ms)
info: Aug-02-2025 01:25:01: 	Connection pool stats:
info: Aug-02-2025 01:25:01: 	Database health check passed (58ms)
error: Aug-02-2025 01:25:23: 	FAILED REQUEST [u9zpe]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:25:26: 	SLOW REQUEST [5wurra]: POST /api/users took 3288ms
info: Aug-02-2025 01:25:31: 	Database health check passed (52ms)
warn: Aug-02-2025 01:25:56: 	SLOW REQUEST [klzbcf]: POST /api/update-fcm-token took 3347ms
warn: Aug-02-2025 01:25:56: 	SLOW REQUEST [cugttj]: GET /api/categories took 3804ms
warn: Aug-02-2025 01:25:56: 	SLOW REQUEST [1cxk1l]: POST /api/list-posts took 3856ms
warn: Aug-02-2025 01:25:56: 	SLOW REQUEST [8i1pi9]: GET /api/getfunds/58422 took 3931ms
info: Aug-02-2025 01:26:01: 	Connection pool stats:
info: Aug-02-2025 01:26:01: 	Database health check passed (52ms)
error: Aug-02-2025 01:26:14: 	FAILED REQUEST [5h9ymq]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:26:29: 	SLOW REQUEST [2sp1hf]: GET /api/getfunds/58422 took 2043ms
warn: Aug-02-2025 01:26:30: 	SLOW REQUEST [kifuvw]: GET /api/getfunds/58422 took 2164ms
info: Aug-02-2025 01:26:31: 	Database health check passed (55ms)
error: Aug-02-2025 01:26:38: 	FAILED REQUEST [h03a0g]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:26:41: 	SLOW REQUEST [hn5t4w]: POST /api/users took 3173ms
info: Aug-02-2025 01:27:01: 	Connection pool stats:
info: Aug-02-2025 01:27:01: 	Database health check passed (49ms)
info: Aug-02-2025 01:27:31: 	Database health check passed (57ms)
info: Aug-02-2025 01:28:01: 	Connection pool stats:
info: Aug-02-2025 01:28:01: 	Database health check passed (60ms)
error: Aug-02-2025 01:28:21: 	FAILED REQUEST [48ds9o]: GET /api/check-premium/58422 returned 400
warn: Aug-02-2025 01:28:24: 	SLOW REQUEST [0wr58]: POST /api/users took 3203ms
info: Aug-02-2025 01:28:31: 	Database health check passed (48ms)
info: Aug-02-2025 01:29:01: 	Connection pool stats:
info: Aug-02-2025 01:29:01: 	Database health check passed (52ms)
info: Aug-02-2025 01:29:31: 	Database health check passed (50ms)
info: Aug-02-2025 01:30:01: 	Connection pool stats:
info: Aug-02-2025 01:30:01: 	Database health check passed (51ms)
info: Aug-02-2025 01:30:31: 	Database health check passed (50ms)
info: Aug-02-2025 01:31:01: 	Connection pool stats:
info: Aug-02-2025 01:31:01: 	Database health check passed (55ms)
info: Aug-02-2025 01:31:31: 	Database health check passed (64ms)
info: Aug-02-2025 01:32:01: 	Connection pool stats:
info: Aug-02-2025 01:32:01: 	Database health check passed (47ms)
info: Aug-02-2025 01:32:31: 	Database health check passed (47ms)
info: Aug-02-2025 01:33:01: 	Connection pool stats:
info: Aug-02-2025 01:33:01: 	Database health check passed (51ms)
info: Aug-02-2025 01:33:37: 	Performance monitoring started
info: Aug-02-2025 01:33:46: 	Starting database health monitoring
info: Aug-02-2025 01:33:46: 	Database health check passed (244ms)
info: Aug-02-2025 01:34:16: 	Database health check passed (54ms)
info: Aug-02-2025 01:34:35: 	Performance monitoring started
info: Aug-02-2025 01:34:37: 	Starting database health monitoring
info: Aug-02-2025 01:34:37: 	Database health check passed (211ms)
info: Aug-02-2025 01:35:07: 	Database health check passed (184ms)
info: Aug-02-2025 01:35:23: 	Performance monitoring started
info: Aug-02-2025 01:35:25: 	Starting database health monitoring
info: Aug-02-2025 01:35:26: 	Database health check passed (804ms)
info: Aug-02-2025 01:35:39: 	Performance monitoring started
info: Aug-02-2025 01:35:41: 	Starting database health monitoring
info: Aug-02-2025 01:35:42: 	Database health check passed (961ms)
info: Aug-02-2025 01:36:11: 	Database health check passed (306ms)
info: Aug-02-2025 01:36:41: 	Connection pool stats:
info: Aug-02-2025 01:36:41: 	Database health check passed (232ms)
info: Aug-02-2025 01:36:53: 	Performance monitoring started
info: Aug-02-2025 01:36:55: 	Starting database health monitoring
info: Aug-02-2025 01:36:57: 	Database health check passed (2073ms)
info: Aug-02-2025 01:37:14: 	Performance monitoring started
info: Aug-02-2025 01:37:16: 	Starting database health monitoring
info: Aug-02-2025 01:37:18: 	Database health check passed (1699ms)
warn: Aug-02-2025 01:37:30: 	SLOW REQUEST [ft2d27]: GET /api/getfunds/58422 took 3796ms
warn: Aug-02-2025 01:37:30: 	SLOW REQUEST [2fu9q]: POST /api/get-user-data took 2515ms
